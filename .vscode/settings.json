{
  "editor.detectIndentation": false,
  "editor.tabSize": 2,
  // "editor.fontSize": 14,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "never"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // Eslint
  "eslint.workingDirectories": ["src"],
  "eslint.validate": ["javascript", "typescript", "javascriptreact", "typescriptreact"],
  "eslint.format.enable": true,
  "[javascript][typescript][typescriptreact]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
    // "editor.codeActionsOnSave": ["source.addMissingImports", "source.fixAll.eslint"]
  },
  "prettier.resolveGlobalModules": true,
  "typescript.tsdk": "node_modules/typescript/lib",
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
