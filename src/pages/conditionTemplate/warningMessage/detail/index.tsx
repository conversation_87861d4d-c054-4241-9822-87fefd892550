import { useEffect, useMemo } from 'react';
import {
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Col, Row, Table } from 'antd';
import { DetailType } from '..';
import Service from '../service';
import { StrategyPatrolType, StrategyPatrolTypeCN, TaskSubType, TaskSubTypeCN } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { computePtTimeStr, parsePtTimeStr } from '@/utils/time';

// CTODO:
const init = {
  name: 'dafds',
  messageTitle: 'dafdsaf',
  messageContent: 'dafdfadsfa',
  rules: [
    {
      timeInterval: 'PT44H',
    },
    {
      timeInterval: 'PT23H',
    },
  ],
};

const timeOpt = [
  {
    label: '8点',
    value: 8,
  },
  {
    label: '10点',
    value: 10,
  },
  {
    label: '12点',
    value: 12,
  },
  {
    label: '14点',
    value: 14,
  },
  {
    label: '16点',
    value: 16,
  },
  {
    label: '18点',
    value: 18,
  },
  {
    label: '20点',
    value: 20,
  },
];

export default function DetailIndex() {
  const [form] = ProForm.useForm();
  const [params] = useQuerySearchParams();
  const navigate = useGlobalNavigate();
  const [service, executeRequest] = Service();

  const disabled = useMemo(() => {
    if (!params?.operateType || params?.operateType === DetailType.Readonly) {
      return true;
    }

    return false;
  }, [params?.operateType]);

  useEffect(() => {
    if (params?.id) {
      const { rules, name, ...rest } = init || {};

      const _rules = rules?.map((r) => {
        const _timeInterval = parsePtTimeStr(r?.timeInterval);

        const [days, hours] = _timeInterval || [];

        return {
          timeInterval: {
            days,
            hours,
            unit: days ? 'Days' : 'Hours',
          },
        };
      });

      form.setFieldsValue({
        ...rest,
        rules: _rules,
        name: params?.operateType === DetailType.Copy ? `${name} 【副本】` : name,
        // CTODO: id
      });
    }
  }, [form, params]);

  return (
    <ProCard>
      <ProForm
        form={form}
        layout="horizontal"
        labelCol={{ span: 2 }}
        disabled={disabled}
        onFinish={(values) => {
          const { rules, ...rest } = values;

          const _rules = rules?.map((r) => {
            const { timeInterval } = r || {};

            return {
              timeInterval: computePtTimeStr([timeInterval?.days || 0, timeInterval?.hours || 0]),
            };
          });

          const payload = {
            ...rest,
            rules: _rules,
          };
        }}
      >
        <ProFormText
          name="name"
          label="消息模版名称"
          width="md"
          rules={[{ required: true, message: '请输入消息模版名称' }]}
        />
        <ProFormSelect
          name="taskType"
          label="关联任务类型"
          width="md"
          rules={[{ required: true, message: '请选择关联任务类型' }]}
          valueEnum={TaskSubTypeCN}
        />
        <ProFormDependency name={['taskType']}>
          {({ taskType }) => {
            if (taskType === TaskSubType.PATROL) {
              return (
                <ProFormSelect
                  label="子任务类型"
                  name="taskSubType"
                  rules={[
                    {
                      required: true,
                      message: '请选择子任务类型',
                    },
                  ]}
                  options={Object.keys(StrategyPatrolTypeCN)
                    .filter(
                      (key) =>
                        ![
                          StrategyPatrolType.VIDEO,
                          StrategyPatrolType.FOOD_SAFETY_NORMAL,
                          StrategyPatrolType.FOOD_SAFETY_VIDEO,
                        ].includes(key as StrategyPatrolType),
                    )
                    .map((key: string) => ({
                      label: StrategyPatrolTypeCN[key],
                      value: key,
                    }))}
                  width="md"
                />
              );
            }

            if ([TaskSubType.REVIEW, TaskSubType.ISSUE].includes(taskType as TaskSubType)) {
              return (
                <ProFormSelect
                  label="子任务类型"
                  name="taskSubType"
                  rules={[
                    {
                      required: true,
                      message: '请选择子任务类型',
                    },
                  ]}
                  options={Object.keys(StrategyPatrolTypeCN)
                    .filter(
                      (key) =>
                        ![
                          StrategyPatrolType.VIDEO,
                          StrategyPatrolType.FOOD_SAFETY_NORMAL,
                          StrategyPatrolType.FOOD_SAFETY_VIDEO,
                        ].includes(key as StrategyPatrolType),
                    )
                    .map((key: string) => ({
                      label: StrategyPatrolTypeCN[key],
                      value: key,
                    }))}
                  width="md"
                />
              );
            }

            return null;
          }}
        </ProFormDependency>
        <ProFormText
          name="messageTitle"
          label="消息名称"
          width="md"
          rules={[{ required: true, message: '请输入消息名称' }]}
        />

        <Row>
          <Col span={8}>
            <ProFormTextArea
              width="md"
              label="消息内容"
              name="messageContent"
              placeholder="请输入消息内容"
              rules={[
                {
                  required: true,
                  message: '请输入消息内容',
                },
              ]}
              fieldProps={{
                maxLength: 400,
                showCount: true,
                autoSize: {
                  minRows: 6,
                },
              }}
              labelCol={{ span: 6 }}
            />
          </Col>
          <Col>
            <p>
              <span>填写说明：</span>
              请将以下所列字段对应的变量码值复制到左侧输入框中，请注意复制完整内容。
            </p>
            <Table
              className="message-setting-variables"
              columns={[
                {
                  title: '字段名称',
                  dataIndex: 'name',
                },
                {
                  title: '变量码值',
                  dataIndex: 'symbol',
                },
              ]}
              dataSource={[]}
            />
          </Col>
        </Row>
        <ProFormList
          name="rules"
          alwaysShowItemLabel
          initialValue={[{}]} // 添加默认一行数据
          creatorButtonProps={{
            creatorButtonText: '新增预警节点',
          }}
          min={1}
          max={10}
        >
          {(f, index, action) => {
            return (
              <>
                <ProForm.Item
                  label={`任务${index === 0 ? '初' : ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'][index]}次预警提前时间`}
                  required
                >
                  <ProFormDependency name={['timeInterval']}>
                    {({ timeInterval }) => {
                      const maxDays = timeInterval?.unit === 'Days' ? 31 : 23;

                      return (
                        <ProFormDigit
                          name={timeInterval?.unit === 'Days' ? ['timeInterval', 'days'] : ['timeInterval', 'hours']}
                          min={1}
                          max={maxDays}
                          rules={[
                            { required: true, message: '请输入正整数' },
                            {
                              validator: (_, value) => {
                                if (value > maxDays) {
                                  return Promise.reject(`不能大于${maxDays}`);
                                }

                                return Promise.resolve();
                              },
                            },
                          ]}
                          noStyle
                          width="xs"
                        />
                      );
                    }}
                  </ProFormDependency>

                  <ProFormSelect
                    name={['timeInterval', 'unit']}
                    options={[
                      {
                        label: '天',
                        value: 'Days',
                      },
                      {
                        label: '小时',
                        value: 'Hours',
                      },
                    ]}
                    initialValue={'Days'}
                    noStyle
                    width="xs"
                    rules={[{ required: true, message: '请选择' }]}
                  />
                </ProForm.Item>
                <ProFormDependency name={['timeInterval']}>
                  {({ timeInterval }) => {
                    if (timeInterval?.unit === 'Days') {
                      return (
                        <ProFormSelect
                          options={timeOpt}
                          label="提醒时间点"
                          name={['timeInterval', 'hours']}
                          rules={[{ required: true, message: '请选择提醒时间点' }]}
                        />
                      );
                    }

                    return null;
                  }}
                </ProFormDependency>
              </>
            );
          }}
        </ProFormList>
      </ProForm>
    </ProCard>
  );
}
