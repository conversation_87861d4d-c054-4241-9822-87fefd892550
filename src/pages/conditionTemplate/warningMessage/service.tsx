import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getUserInfoList } from '@/http/apis/center-control';
import {
  disableMessageWarningTemplate,
  enableMessageWarningTemplate,
  getMessageWarningTemplateList,
} from '@/http/apis/task-center';
import { formatUserListToRoleOptions } from '@/utils/format';

export enum RequestName {
  GetMessageWarningTemplateList,
  DisableMessageWarningTemplate,
  EnableMessageWarningTemplate,
  GetUserInfoList,
}

export const initState: any = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetUserInfoList]: {
      beforeRequest: (dispatch) => {
        dispatch({ reviewerLoading: true });
      },
      request: HttpAop(getUserInfoList, {
        after: [(v) => formatUserListToRoleOptions(v, true)],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ reviewerOptions: data, reviewerLoading: false });
      },
    },
    [RequestName.GetMessageWarningTemplateList]: {
      request: getMessageWarningTemplateList,
    },
    [RequestName.DisableMessageWarningTemplate]: {
      request: disableMessageWarningTemplate,
    },
    [RequestName.EnableMessageWarningTemplate]: {
      request: enableMessageWarningTemplate,
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
